// This file is auto-generated by @hey-api/openapi-ts

export type BodyPromptGeneratorCreatePromptSession = {
    subjects?: string | null;
    action?: string | null;
    environment?: string | null;
    composition?: string | null;
    angle?: string | null;
    camera_movements?: Array<string> | null;
    visual_aesthetic?: string | null;
    lighting_design?: string | null;
    sound_design?: string | null;
    character_reference_url?: string | null;
    negative_prompt?: string | null;
    seed?: number | null;
};

export type BodyPromptGeneratorGetPromptSession = {
    session_id: string;
};

export type BodyPromptGeneratorGetRecentSessions = {
    limit: number;
};

export type BodyPromptGeneratorUpdatePromptSession = {
    session_id: string;
    subjects?: string | null;
    action?: string | null;
    environment?: string | null;
    composition?: string | null;
    angle?: string | null;
    camera_movements?: Array<string> | null;
    visual_aesthetic?: string | null;
    lighting_design?: string | null;
    sound_design?: string | null;
    character_reference_url?: string | null;
    negative_prompt?: string | null;
    seed?: number | null;
};

export type HttpValidationError = {
    detail?: Array<ValidationError>;
};

export type PromptSession = {
    id?: string;
    subjects?: string | null;
    action?: string | null;
    environment?: string | null;
    composition?: string | null;
    angle?: string | null;
    camera_movements?: Array<string>;
    visual_aesthetic?: string | null;
    lighting_design?: string | null;
    sound_design?: string | null;
    character_reference_url?: string | null;
    negative_prompt?: string | null;
    seed?: number | null;
    generated_prompt?: string | null;
    created_at?: string;
    last_updated?: string;
};

export type ValidationError = {
    loc: Array<string | number>;
    msg: string;
    type: string;
};

export type PromptGeneratorCreatePromptSessionData = {
    body: BodyPromptGeneratorCreatePromptSession;
    path?: never;
    query?: never;
    url: '/api/prompt_generator/create_prompt_session';
};

export type PromptGeneratorCreatePromptSessionErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type PromptGeneratorCreatePromptSessionError = PromptGeneratorCreatePromptSessionErrors[keyof PromptGeneratorCreatePromptSessionErrors];

export type PromptGeneratorCreatePromptSessionResponses = {
    /**
     * Successful Response
     */
    200: PromptSession;
};

export type PromptGeneratorCreatePromptSessionResponse = PromptGeneratorCreatePromptSessionResponses[keyof PromptGeneratorCreatePromptSessionResponses];

export type PromptGeneratorGetPromptSessionData = {
    body: BodyPromptGeneratorGetPromptSession;
    path?: never;
    query?: never;
    url: '/api/prompt_generator/get_prompt_session';
};

export type PromptGeneratorGetPromptSessionErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type PromptGeneratorGetPromptSessionError = PromptGeneratorGetPromptSessionErrors[keyof PromptGeneratorGetPromptSessionErrors];

export type PromptGeneratorGetPromptSessionResponses = {
    /**
     * Successful Response
     */
    200: PromptSession | null;
};

export type PromptGeneratorGetPromptSessionResponse = PromptGeneratorGetPromptSessionResponses[keyof PromptGeneratorGetPromptSessionResponses];

export type PromptGeneratorGetRecentSessionsData = {
    body: BodyPromptGeneratorGetRecentSessions;
    path?: never;
    query?: never;
    url: '/api/prompt_generator/get_recent_sessions';
};

export type PromptGeneratorGetRecentSessionsErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type PromptGeneratorGetRecentSessionsError = PromptGeneratorGetRecentSessionsErrors[keyof PromptGeneratorGetRecentSessionsErrors];

export type PromptGeneratorGetRecentSessionsResponses = {
    /**
     * Successful Response
     */
    200: Array<PromptSession>;
};

export type PromptGeneratorGetRecentSessionsResponse = PromptGeneratorGetRecentSessionsResponses[keyof PromptGeneratorGetRecentSessionsResponses];

export type PromptGeneratorUpdatePromptSessionData = {
    body: BodyPromptGeneratorUpdatePromptSession;
    path?: never;
    query?: never;
    url: '/api/prompt_generator/update_prompt_session';
};

export type PromptGeneratorUpdatePromptSessionErrors = {
    /**
     * Validation Error
     */
    422: HttpValidationError;
};

export type PromptGeneratorUpdatePromptSessionError = PromptGeneratorUpdatePromptSessionErrors[keyof PromptGeneratorUpdatePromptSessionErrors];

export type PromptGeneratorUpdatePromptSessionResponses = {
    /**
     * Successful Response
     */
    200: PromptSession;
};

export type PromptGeneratorUpdatePromptSessionResponse = PromptGeneratorUpdatePromptSessionResponses[keyof PromptGeneratorUpdatePromptSessionResponses];