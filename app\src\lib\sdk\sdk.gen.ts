// This file is auto-generated by @hey-api/openapi-ts

import type { Options } from '@hey-api/client-fetch';
import type { PromptGeneratorCreatePromptSessionData, PromptGeneratorCreatePromptSessionResponse, PromptGeneratorCreatePromptSessionError, PromptGeneratorGetPromptSessionData, PromptGeneratorGetPromptSessionResponse, PromptGeneratorGetPromptSessionError, PromptGeneratorGetRecentSessionsData, PromptGeneratorGetRecentSessionsResponse, PromptGeneratorGetRecentSessionsError, PromptGeneratorUpdatePromptSessionData, PromptGeneratorUpdatePromptSessionResponse, PromptGeneratorUpdatePromptSessionError } from './types.gen';
import { client as _heyApiClient } from './client.gen';

/**
 * Prompt Generator Create Prompt Session
 * Create a new prompt session with the provided parameters.
 */
export const promptGeneratorCreatePromptSession = <ThrowOnError extends boolean = false>(options: Options<PromptGeneratorCreatePromptSessionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PromptGeneratorCreatePromptSessionResponse, PromptGeneratorCreatePromptSessionError, ThrowOnError>({
        url: '/api/prompt_generator/create_prompt_session',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Prompt Generator Get Prompt Session
 * Get a prompt session by its ID.
 */
export const promptGeneratorGetPromptSession = <ThrowOnError extends boolean = false>(options: Options<PromptGeneratorGetPromptSessionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PromptGeneratorGetPromptSessionResponse, PromptGeneratorGetPromptSessionError, ThrowOnError>({
        url: '/api/prompt_generator/get_prompt_session',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Prompt Generator Get Recent Sessions
 * Get the most recent prompt sessions.
 */
export const promptGeneratorGetRecentSessions = <ThrowOnError extends boolean = false>(options: Options<PromptGeneratorGetRecentSessionsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PromptGeneratorGetRecentSessionsResponse, PromptGeneratorGetRecentSessionsError, ThrowOnError>({
        url: '/api/prompt_generator/get_recent_sessions',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Prompt Generator Update Prompt Session
 * Update an existing prompt session and regenerate the prompt.
 */
export const promptGeneratorUpdatePromptSession = <ThrowOnError extends boolean = false>(options: Options<PromptGeneratorUpdatePromptSessionData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PromptGeneratorUpdatePromptSessionResponse, PromptGeneratorUpdatePromptSessionError, ThrowOnError>({
        url: '/api/prompt_generator/update_prompt_session',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};